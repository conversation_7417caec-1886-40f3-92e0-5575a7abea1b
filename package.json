{"name": "gps-tracking-system", "version": "1.0.0", "description": "Complete GPS tracking system for 200+ devices", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client-mobile\"", "dev-local": "concurrently \"npm run server\" \"npm run client\"", "server": "nodemon server/index.js", "client": "cd client && npm start", "client-mobile": "cd client && npm run start-mobile", "build": "cd client && npm run build", "start": "node server/index.js", "start-mobile": "concurrently \"npm run server\" \"npm run client-mobile\"", "install-all": "npm install && cd client && npm install"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "@supabase/supabase-js": "^2.38.0", "uuid": "^9.0.1", "compression": "^1.7.4", "helmet": "^7.1.0"}, "devDependencies": {"nodemon": "^3.0.1", "concurrently": "^8.2.2"}, "keywords": ["gps", "tracking", "real-time", "websocket", "maps"], "author": "GPS Tracking System", "license": "MIT"}