{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M16 18v2H8v-2zM11 7.99V16h2V7.99h3L12 4 8 7.99z\"\n}), 'UpgradeOutlined');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/traccar-windows-64-6.7.2/client/node_modules/@mui/icons-material/esm/UpgradeOutlined.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M16 18v2H8v-2zM11 7.99V16h2V7.99h3L12 4 8 7.99z\"\n}), 'UpgradeOutlined');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACrDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}