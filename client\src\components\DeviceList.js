import React, { useState } from 'react';
import {
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Box,
  TextField,
  InputAdornment,
  Tooltip
} from '@mui/material';
import {
  DirectionsCar,
  TwoWheeler,
  LocalShipping,
  Search,
  Battery20,
  Battery50,
  Battery80,
  BatteryFull,
  Speed,
  Navigation
} from '@mui/icons-material';

const DeviceList = ({ devices, selectedDevice, onDeviceSelect }) => {
  const [searchTerm, setSearchTerm] = useState('');

  const getDeviceIcon = (type) => {
    switch (type) {
      case 'car': return <DirectionsCar />;
      case 'motorcycle': return <TwoWheeler />;
      case 'truck': return <LocalShipping />;
      default: return <DirectionsCar />;
    }
  };

  const getStatusColor = (device) => {
    if (!device.lastSeen) return 'error';
    
    const lastSeen = new Date(device.lastSeen);
    const now = new Date();
    const diffMinutes = (now - lastSeen) / (1000 * 60);
    
    if (diffMinutes < 5) return 'success';
    if (diffMinutes < 30) return 'warning';
    return 'error';
  };

  const getStatusText = (device) => {
    if (!device.lastSeen) return 'OFFLINE';
    
    const lastSeen = new Date(device.lastSeen);
    const now = new Date();
    const diffMinutes = (now - lastSeen) / (1000 * 60);
    
    if (diffMinutes < 5) return 'ONLINE';
    if (diffMinutes < 30) return 'IDLE';
    return 'OFFLINE';
  };

  const getBatteryIcon = (battery) => {
    if (battery > 80) return <BatteryFull color="success" />;
    if (battery > 50) return <Battery80 color="warning" />;
    if (battery > 20) return <Battery50 color="warning" />;
    return <Battery20 color="error" />;
  };

  const formatLastSeen = (lastSeen) => {
    if (!lastSeen) return 'Never';
    
    const date = new Date(lastSeen);
    const now = new Date();
    const diffMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  const filteredDevices = devices.filter(device =>
    device.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (device.name && device.name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const sortedDevices = filteredDevices.sort((a, b) => {
    // Sort by status (online first), then by name
    const statusA = getStatusText(a);
    const statusB = getStatusText(b);
    
    if (statusA !== statusB) {
      if (statusA === 'ONLINE') return -1;
      if (statusB === 'ONLINE') return 1;
      if (statusA === 'IDLE') return -1;
      if (statusB === 'IDLE') return 1;
    }
    
    return (a.name || a.id).localeCompare(b.name || b.id);
  });

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Typography variant="h6" gutterBottom>
        Devices ({devices.length})
      </Typography>
      
      <TextField
        size="small"
        placeholder="Search devices..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Search />
            </InputAdornment>
          ),
        }}
        sx={{ mb: 2 }}
      />
      
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <List dense>
          {sortedDevices.map((device) => (
            <ListItem
              key={device.id}
              button
              selected={selectedDevice && selectedDevice.id === device.id}
              onClick={() => onDeviceSelect(device)}
              sx={{
                border: '1px solid rgba(255,255,255,0.1)',
                borderRadius: 1,
                mb: 1,
                '&.Mui-selected': {
                  backgroundColor: 'rgba(25, 118, 210, 0.2)',
                  '&:hover': {
                    backgroundColor: 'rgba(25, 118, 210, 0.3)',
                  },
                },
              }}
            >
              <ListItemIcon>
                {getDeviceIcon(device.type)}
              </ListItemIcon>
              
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" noWrap>
                      {device.name || device.id}
                    </Typography>
                    <Chip
                      label={getStatusText(device)}
                      size="small"
                      color={getStatusColor(device)}
                      variant="outlined"
                    />
                  </Box>
                }
                secondary={
                  <Box sx={{ mt: 0.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                      <Speed fontSize="small" />
                      <Typography variant="caption">
                        {device.speed?.toFixed(1) || 0} km/h
                      </Typography>
                      
                      <Navigation fontSize="small" />
                      <Typography variant="caption">
                        {device.heading?.toFixed(0) || 0}°
                      </Typography>
                      
                      <Tooltip title={`Battery: ${device.battery || 0}%`}>
                        {getBatteryIcon(device.battery || 0)}
                      </Tooltip>
                    </Box>
                    
                    <Typography variant="caption" color="text.secondary">
                      Last seen: {formatLastSeen(device.lastSeen)}
                    </Typography>
                    
                    {device.location && (
                      <Typography variant="caption" color="text.secondary" display="block">
                        {device.location.latitude.toFixed(4)}, {device.location.longitude.toFixed(4)}
                      </Typography>
                    )}
                  </Box>
                }
              />
            </ListItem>
          ))}
          
          {sortedDevices.length === 0 && (
            <ListItem>
              <ListItemText
                primary="No devices found"
                secondary={searchTerm ? "Try adjusting your search" : "No devices are currently connected"}
              />
            </ListItem>
          )}
        </List>
      </Box>
    </Box>
  );
};

export default DeviceList;
