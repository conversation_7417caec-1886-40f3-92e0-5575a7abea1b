const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const compression = require('compression');
const helmet = require('helmet');
const path = require('path');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../client/build')));

// In-memory storage for demo (in production, use database)
const devices = new Map();
const locationHistory = new Map();

// Device management
app.get('/api/devices', (req, res) => {
  const deviceList = Array.from(devices.values());
  res.json(deviceList);
});

app.post('/api/devices', (req, res) => {
  const { deviceId, name, type } = req.body;
  const device = {
    id: deviceId,
    name: name || `Device ${deviceId}`,
    type: type || 'vehicle',
    status: 'offline',
    lastSeen: null,
    location: null,
    speed: 0,
    heading: 0,
    battery: 100
  };
  devices.set(deviceId, device);
  res.json(device);
});

// GPS data endpoint
app.post('/api/gps', (req, res) => {
  const { deviceId, latitude, longitude, speed, heading, timestamp, battery } = req.body;
  
  if (!deviceId || !latitude || !longitude) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  const location = {
    deviceId,
    latitude: parseFloat(latitude),
    longitude: parseFloat(longitude),
    speed: parseFloat(speed) || 0,
    heading: parseFloat(heading) || 0,
    timestamp: timestamp || new Date().toISOString(),
    battery: parseInt(battery) || 100
  };

  // Update device
  const device = devices.get(deviceId) || {
    id: deviceId,
    name: `Device ${deviceId}`,
    type: 'vehicle'
  };
  
  device.status = 'online';
  device.lastSeen = location.timestamp;
  device.location = location;
  device.speed = location.speed;
  device.heading = location.heading;
  device.battery = location.battery;
  
  devices.set(deviceId, device);

  // Store location history
  if (!locationHistory.has(deviceId)) {
    locationHistory.set(deviceId, []);
  }
  const history = locationHistory.get(deviceId);
  history.push(location);
  
  // Keep only last 1000 points per device
  if (history.length > 1000) {
    history.shift();
  }

  // Broadcast to all connected clients
  io.emit('locationUpdate', {
    device,
    location
  });

  res.json({ success: true, location });
});

// Get location history
app.get('/api/devices/:deviceId/history', (req, res) => {
  const { deviceId } = req.params;
  const history = locationHistory.get(deviceId) || [];
  res.json(history);
});

// WebSocket connections
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  // Send current devices to new client
  socket.emit('devicesUpdate', Array.from(devices.values()));
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Serve React app (only if build exists, otherwise show API info)
app.get('*', (req, res) => {
  const buildPath = path.join(__dirname, '../client/build/index.html');

  // Check if build exists
  const fs = require('fs');
  if (fs.existsSync(buildPath)) {
    res.sendFile(buildPath);
  } else {
    // Show API information if build doesn't exist
    res.json({
      message: 'GPS Partizan API Server',
      status: 'Running',
      endpoints: {
        devices: 'GET /api/devices',
        gps: 'POST /api/gps',
        history: 'GET /api/devices/:deviceId/history'
      },
      frontend: 'React development server should be running on port 3000',
      note: 'This is the API server. The frontend is served separately during development.'
    });
  }
});

const PORT = process.env.PORT || 3001;
const HOST = process.env.HOST || '0.0.0.0'; // Bind to all network interfaces

server.listen(PORT, HOST, () => {
  console.log(`GPS Partizan Server running on ${HOST}:${PORT}`);
  console.log(`Local Dashboard: http://localhost:${PORT}`);

  // Get and display local IP addresses
  const os = require('os');
  const networkInterfaces = os.networkInterfaces();
  const localIPs = [];

  Object.keys(networkInterfaces).forEach(interfaceName => {
    networkInterfaces[interfaceName].forEach(interface => {
      if (interface.family === 'IPv4' && !interface.internal) {
        localIPs.push(interface.address);
      }
    });
  });

  if (localIPs.length > 0) {
    console.log('\n🌐 Mobile Access URLs:');
    localIPs.forEach(ip => {
      console.log(`   📱 http://${ip}:${PORT}`);
    });
    console.log('\n📋 Share these URLs with anyone to access the GPS Partizan dashboard on mobile devices!');
  }
});

module.exports = { app, server, io };
