import React, { useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Box, AppBar, Toolbar, Typography, Grid, Paper, Chip } from '@mui/material';
import GPSMap from './components/GPSMap';
import DeviceList from './components/DeviceList';
import Statistics from './components/Statistics';
import io from 'socket.io-client';
import './App.css';

const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

function App() {
  const [devices, setDevices] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [, setSocket] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('Disconnected');

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io(process.env.REACT_APP_SERVER_URL || 'http://localhost:3001');
    setSocket(newSocket);

    newSocket.on('connect', () => {
      console.log('Connected to server');
      setConnectionStatus('Connected');
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
      setConnectionStatus('Disconnected');
    });

    newSocket.on('devicesUpdate', (deviceList) => {
      console.log('Devices updated:', deviceList.length);
      setDevices(deviceList);
    });

    newSocket.on('locationUpdate', (data) => {
      const { device } = data;
      setDevices(prevDevices => {
        const updatedDevices = [...prevDevices];
        const deviceIndex = updatedDevices.findIndex(d => d.id === device.id);

        if (deviceIndex >= 0) {
          updatedDevices[deviceIndex] = device;
        } else {
          updatedDevices.push(device);
        }

        return updatedDevices;
      });
    });

    return () => {
      newSocket.close();
    };
  }, []);

  const handleDeviceSelect = (device) => {
    setSelectedDevice(device);
  };

  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <Box sx={{ flexGrow: 1, height: '100vh', display: 'flex', flexDirection: 'column' }}>
        <AppBar position="static">
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              GPS Tracking System - {devices.length} Devices
            </Typography>
            <Chip
              label={connectionStatus}
              color={connectionStatus === 'Connected' ? 'success' : 'error'}
              variant="outlined"
            />
          </Toolbar>
        </AppBar>

        <Box sx={{ flexGrow: 1, p: 2 }}>
          <Grid container spacing={2} sx={{ height: '100%' }}>
            <Grid item xs={12} md={8} sx={{ height: '100%' }}>
              <Paper sx={{ height: '100%', p: 1 }}>
                <GPSMap
                  devices={devices}
                  selectedDevice={selectedDevice}
                  onDeviceSelect={handleDeviceSelect}
                />
              </Paper>
            </Grid>

            <Grid item xs={12} md={4} sx={{ height: '100%' }}>
              <Grid container spacing={2} sx={{ height: '100%' }}>
                <Grid item xs={12} sx={{ height: '40%' }}>
                  <Paper sx={{ height: '100%', p: 2 }}>
                    <Statistics devices={devices} />
                  </Paper>
                </Grid>

                <Grid item xs={12} sx={{ height: '60%' }}>
                  <Paper sx={{ height: '100%', p: 1 }}>
                    <DeviceList
                      devices={devices}
                      selectedDevice={selectedDevice}
                      onDeviceSelect={handleDeviceSelect}
                    />
                  </Paper>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </ThemeProvider>
  );
}

export default App;
