import React, { useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Box, AppBar, Toolbar, Typography, Grid, Paper, Chip, useMediaQuery } from '@mui/material';
import GPSMap from './components/GPSMap';
import DeviceList from './components/DeviceList';
import Statistics from './components/Statistics';
import io from 'socket.io-client';
import './App.css';

const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

function App() {
  const [devices, setDevices] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [, setSocket] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('Disconnected');

  // Mobile responsiveness
  const isMobile = useMediaQuery('(max-width:768px)');
  const isTablet = useMediaQuery('(max-width:1024px)');

  useEffect(() => {
    // Automatically detect server URL for mobile access
    const getServerUrl = () => {
      if (process.env.REACT_APP_SERVER_URL && process.env.REACT_APP_SERVER_URL.trim()) {
        return process.env.REACT_APP_SERVER_URL;
      }

      // For mobile access, use the current host with port 3001
      const protocol = window.location.protocol;
      const hostname = window.location.hostname;

      // If accessing via IP address or domain, use that for the server connection
      if (hostname !== 'localhost' && hostname !== '127.0.0.1') {
        return `${protocol}//${hostname}:3001`;
      }

      // Default to localhost for development
      return 'http://localhost:3001';
    };

    const serverUrl = getServerUrl();
    console.log('Connecting to server:', serverUrl);

    // Initialize socket connection
    const newSocket = io(serverUrl);
    setSocket(newSocket);

    newSocket.on('connect', () => {
      console.log('Connected to server');
      setConnectionStatus('Connected');
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
      setConnectionStatus('Disconnected');
    });

    newSocket.on('devicesUpdate', (deviceList) => {
      console.log('Devices updated:', deviceList.length);
      setDevices(deviceList);
    });

    newSocket.on('locationUpdate', (data) => {
      const { device } = data;
      setDevices(prevDevices => {
        const updatedDevices = [...prevDevices];
        const deviceIndex = updatedDevices.findIndex(d => d.id === device.id);

        if (deviceIndex >= 0) {
          updatedDevices[deviceIndex] = device;
        } else {
          updatedDevices.push(device);
        }

        return updatedDevices;
      });
    });

    return () => {
      newSocket.close();
    };
  }, []);

  const handleDeviceSelect = (device) => {
    setSelectedDevice(device);
  };

  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <Box sx={{ flexGrow: 1, height: '100vh', display: 'flex', flexDirection: 'column' }}>
        <AppBar position="static">
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              GPS Tracking System - {devices.length} Devices
            </Typography>
            <Chip
              label={connectionStatus}
              color={connectionStatus === 'Connected' ? 'success' : 'error'}
              variant="outlined"
            />
          </Toolbar>
        </AppBar>

        <Box sx={{ flexGrow: 1, p: isMobile ? 1 : 2 }}>
          <Grid container spacing={isMobile ? 1 : 2} sx={{ height: '100%' }}>
            {/* Mobile Layout: Stack vertically */}
            {isMobile ? (
              <>
                {/* Map takes full width on mobile */}
                <Grid item xs={12} sx={{ height: '50vh' }}>
                  <Paper sx={{ height: '100%', p: 0.5 }}>
                    <GPSMap
                      devices={devices}
                      selectedDevice={selectedDevice}
                      onDeviceSelect={handleDeviceSelect}
                    />
                  </Paper>
                </Grid>

                {/* Statistics row on mobile */}
                <Grid item xs={12} sx={{ height: '15vh' }}>
                  <Paper sx={{ height: '100%', p: 1 }}>
                    <Statistics devices={devices} />
                  </Paper>
                </Grid>

                {/* Device list on mobile */}
                <Grid item xs={12} sx={{ height: '35vh' }}>
                  <Paper sx={{ height: '100%', p: 0.5 }}>
                    <DeviceList
                      devices={devices}
                      selectedDevice={selectedDevice}
                      onDeviceSelect={handleDeviceSelect}
                    />
                  </Paper>
                </Grid>
              </>
            ) : (
              /* Desktop/Tablet Layout */
              <>
                <Grid item xs={12} md={isTablet ? 12 : 8} sx={{ height: isTablet ? '60%' : '100%' }}>
                  <Paper sx={{ height: '100%', p: 1 }}>
                    <GPSMap
                      devices={devices}
                      selectedDevice={selectedDevice}
                      onDeviceSelect={handleDeviceSelect}
                    />
                  </Paper>
                </Grid>

                <Grid item xs={12} md={isTablet ? 12 : 4} sx={{ height: isTablet ? '40%' : '100%' }}>
                  <Grid container spacing={2} sx={{ height: '100%' }}>
                    <Grid item xs={isTablet ? 6 : 12} sx={{ height: isTablet ? '100%' : '40%' }}>
                      <Paper sx={{ height: '100%', p: 2 }}>
                        <Statistics devices={devices} />
                      </Paper>
                    </Grid>

                    <Grid item xs={isTablet ? 6 : 12} sx={{ height: isTablet ? '100%' : '60%' }}>
                      <Paper sx={{ height: '100%', p: 1 }}>
                        <DeviceList
                          devices={devices}
                          selectedDevice={selectedDevice}
                          onDeviceSelect={handleDeviceSelect}
                        />
                      </Paper>
                    </Grid>
                  </Grid>
                </Grid>
              </>
            )}
          </Grid>
        </Box>
      </Box>
    </ThemeProvider>
  );
}

export default App;
