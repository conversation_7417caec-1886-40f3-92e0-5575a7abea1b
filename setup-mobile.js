const os = require('os');
const { exec } = require('child_process');

console.log('🌐 GPS Tracking System - Mobile Setup');
console.log('=====================================\n');

// Get local IP addresses
function getLocalIPs() {
  const networkInterfaces = os.networkInterfaces();
  const localIPs = [];
  
  Object.keys(networkInterfaces).forEach(interfaceName => {
    networkInterfaces[interfaceName].forEach(interface => {
      if (interface.family === 'IPv4' && !interface.internal) {
        localIPs.push(interface.address);
      }
    });
  });
  
  return localIPs;
}

const localIPs = getLocalIPs();

if (localIPs.length === 0) {
  console.log('❌ No network interfaces found.');
  console.log('Make sure you are connected to a network (WiFi or Ethernet).');
  process.exit(1);
}

console.log('📱 Mobile Access URLs:');
console.log('======================');
localIPs.forEach((ip, index) => {
  console.log(`${index + 1}. Frontend: http://${ip}:3000`);
  console.log(`   Backend:  http://${ip}:3001`);
  console.log('');
});

console.log('📋 Instructions:');
console.log('================');
console.log('1. Make sure your mobile device is on the same WiFi network');
console.log('2. Open any of the URLs above on your mobile device');
console.log('3. The dashboard will automatically connect to the correct server');
console.log('4. Share these URLs with anyone to give them access\n');

console.log('🔧 Starting servers for mobile access...');
console.log('==========================================');

// Start the mobile-optimized servers
const startCommand = process.platform === 'win32' ? 'npm run start-mobile' : 'npm run start-mobile';

exec(startCommand, (error, stdout, stderr) => {
  if (error) {
    console.error(`Error starting servers: ${error}`);
    return;
  }
  console.log(stdout);
  if (stderr) {
    console.error(stderr);
  }
});

console.log('\n✅ Setup complete! Use Ctrl+C to stop the servers.');
