# GPS Tracking System

A complete real-time GPS tracking system capable of monitoring 200+ GPS devices simultaneously.

## Features

- **Real-time tracking** of 200+ GPS devices
- **Interactive map** with device clustering and real-time updates
- **WebSocket communication** for instant location updates
- **Device management** with status monitoring
- **Statistics dashboard** with system metrics
- **GPS simulator** for testing with realistic movement patterns
- **Responsive design** with dark theme
- **Battery monitoring** and low battery alerts
- **Speed and heading tracking**
- **Historical location data**

## System Architecture

### Backend (Node.js/Express)
- RESTful API for device management
- WebSocket server for real-time updates
- In-memory storage (easily replaceable with database)
- GPS data ingestion endpoint

### Frontend (React)
- Interactive map using Leaflet
- Real-time device list with search and filtering
- Statistics dashboard
- Material-UI components with dark theme
- WebSocket client for live updates

### GPS Simulator
- Simulates 200 GPS devices with realistic movement
- Multiple city routes (NYC, LA, Chicago, etc.)
- Realistic speed variations and battery simulation
- Configurable update intervals

## Quick Start

### 🖥️ Local Development
```bash
npm run dev-local
```
This starts both server and client for local development only.

### 📱 Mobile Access Setup
```bash
npm run start-mobile
```
This configures the system for mobile access on your network.

**Or use the automated setup:**
```bash
node setup-mobile.js
```

### Manual Setup Steps:

#### 1. Start the Backend Server
```bash
npm run server
```
The server will start and display mobile access URLs.

#### 2. Start the Frontend for Mobile (in a new terminal)
```bash
npm run client-mobile
```
The dashboard will be accessible from any device on your network.

#### 3. Start the GPS Simulator (in a new terminal)
```bash
cd simulator
npm start
```
This will simulate 200 GPS devices sending location data.

### 🌐 Mobile Access URLs
After starting the servers, you'll see URLs like:
- **Frontend**: `http://192.168.1.100:3000` (for mobile access)
- **Backend**: `http://192.168.1.100:3001` (API server)

Share these URLs with anyone to give them access to the GPS dashboard!

## API Endpoints

### Device Management
- `GET /api/devices` - Get all devices
- `POST /api/devices` - Register a new device

### GPS Data
- `POST /api/gps` - Submit GPS location data
- `GET /api/devices/:deviceId/history` - Get location history

### WebSocket Events
- `devicesUpdate` - Initial device list
- `locationUpdate` - Real-time location updates

## GPS Data Format

Send GPS data to `POST /api/gps`:

```json
{
  "deviceId": "GPS_001",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "speed": 45.5,
  "heading": 180,
  "battery": 85,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Configuration

### Environment Variables (.env)
```
PORT=3001
NODE_ENV=development
REACT_APP_SERVER_URL=http://localhost:3001
GPS_SIMULATOR_DEVICE_COUNT=200
GPS_SIMULATOR_UPDATE_INTERVAL=5000
```

## Device Types

The system supports different device types:
- **car** - Regular vehicles (default)
- **motorcycle** - Motorcycles
- **truck** - Large vehicles

## Device Status

- **ONLINE** - Last seen < 5 minutes ago
- **IDLE** - Last seen 5-30 minutes ago  
- **OFFLINE** - Last seen > 30 minutes ago

## Testing

### Test with fewer devices:
```bash
cd simulator
node test-simulator.js
```

### Manual testing:
Send GPS data using curl:
```bash
curl -X POST http://localhost:3001/api/gps \
  -H "Content-Type: application/json" \
  -d '{
    "deviceId": "TEST_001",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "speed": 25,
    "heading": 90,
    "battery": 75
  }'
```

## Performance

The system is optimized for handling 200+ concurrent GPS devices:
- Efficient WebSocket broadcasting
- Map clustering for performance
- Optimized React rendering
- Memory-efficient location history storage

## Customization

### Adding More Routes
Edit `simulator/gps-simulator.js` and add more routes to the `routes` array.

### Changing Update Intervals
Modify the interval in the simulator or set `GPS_SIMULATOR_UPDATE_INTERVAL` environment variable.

### Database Integration
Replace the in-memory storage in `server/index.js` with your preferred database (PostgreSQL, MongoDB, etc.).

## Troubleshooting

1. **Port conflicts**: Change PORT in .env file
2. **Map not loading**: Check internet connection for map tiles
3. **Devices not appearing**: Ensure simulator is running and server is accessible
4. **WebSocket issues**: Check firewall settings and CORS configuration

## License

MIT License - Feel free to use and modify for your projects.
