import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),
  iconUrl: require('leaflet/dist/images/marker-icon.png'),
  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),
});

// Custom icons for different device types
const createCustomIcon = (type, status) => {
  const colors = {
    online: '#4CAF50',
    offline: '#F44336',
    idle: '#FF9800'
  };
  
  const color = colors[status] || colors.offline;
  
  return L.divIcon({
    className: 'custom-div-icon',
    html: `
      <div style="
        background-color: ${color};
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 4px rgba(0,0,0,0.3);
      "></div>
    `,
    iconSize: [16, 16],
    iconAnchor: [8, 8]
  });
};

// Component to handle map updates
function MapUpdater({ devices, selectedDevice }) {
  const map = useMap();
  
  useEffect(() => {
    if (devices.length > 0) {
      const group = new L.featureGroup(
        devices
          .filter(device => device.location)
          .map(device => 
            L.marker([device.location.latitude, device.location.longitude])
          )
      );
      
      if (group.getLayers().length > 0) {
        map.fitBounds(group.getBounds(), { padding: [20, 20] });
      }
    }
  }, [devices, map]);
  
  useEffect(() => {
    if (selectedDevice && selectedDevice.location) {
      map.setView([selectedDevice.location.latitude, selectedDevice.location.longitude], 15);
    }
  }, [selectedDevice, map]);
  
  return null;
}

const GPSMap = ({ devices, selectedDevice, onDeviceSelect }) => {
  const [mapCenter] = useState([39.8283, -98.5795]); // Center of USA
  const [mapZoom] = useState(4);

  const getDeviceStatus = (device) => {
    if (!device.lastSeen) return 'offline';
    
    const lastSeen = new Date(device.lastSeen);
    const now = new Date();
    const diffMinutes = (now - lastSeen) / (1000 * 60);
    
    if (diffMinutes < 5) return 'online';
    if (diffMinutes < 30) return 'idle';
    return 'offline';
  };

  const formatLastSeen = (lastSeen) => {
    if (!lastSeen) return 'Never';
    
    const date = new Date(lastSeen);
    const now = new Date();
    const diffMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  return (
    <div style={{ height: '100%', width: '100%' }}>
      <MapContainer
        center={mapCenter}
        zoom={mapZoom}
        style={{ height: '100%', width: '100%' }}
        zoomControl={true}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        <MapUpdater devices={devices} selectedDevice={selectedDevice} />
        
        {devices
          .filter(device => device.location)
          .map(device => {
            const status = getDeviceStatus(device);
            // const isSelected = selectedDevice && selectedDevice.id === device.id;
            
            return (
              <Marker
                key={device.id}
                position={[device.location.latitude, device.location.longitude]}
                icon={createCustomIcon(device.type, status)}
                eventHandlers={{
                  click: () => onDeviceSelect(device),
                }}
              >
                <Popup>
                  <div style={{ minWidth: '200px' }}>
                    <h3 style={{ margin: '0 0 10px 0', color: '#333' }}>
                      {device.name || device.id}
                    </h3>
                    <div style={{ fontSize: '12px', lineHeight: '1.4' }}>
                      <div><strong>ID:</strong> {device.id}</div>
                      <div><strong>Type:</strong> {device.type}</div>
                      <div><strong>Status:</strong> 
                        <span style={{ 
                          color: status === 'online' ? '#4CAF50' : 
                                status === 'idle' ? '#FF9800' : '#F44336',
                          fontWeight: 'bold',
                          marginLeft: '5px'
                        }}>
                          {status.toUpperCase()}
                        </span>
                      </div>
                      <div><strong>Speed:</strong> {device.speed?.toFixed(1) || 0} km/h</div>
                      <div><strong>Heading:</strong> {device.heading?.toFixed(0) || 0}°</div>
                      <div><strong>Battery:</strong> {device.battery || 0}%</div>
                      <div><strong>Last Seen:</strong> {formatLastSeen(device.lastSeen)}</div>
                      <div><strong>Location:</strong></div>
                      <div style={{ marginLeft: '10px', fontSize: '11px' }}>
                        Lat: {device.location.latitude.toFixed(6)}<br/>
                        Lng: {device.location.longitude.toFixed(6)}
                      </div>
                    </div>
                  </div>
                </Popup>
              </Marker>
            );
          })
        }
      </MapContainer>
    </div>
  );
};

export default GPSMap;
